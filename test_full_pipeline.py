#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试完整的前端到后端参数传递流程
"""

import json
import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

import crawler
import selenium_diver_change

def test_frontend_to_backend_params():
    """测试前端到后端的参数传递"""
    print("=== 测试前端到后端参数传递 ===")
    
    # 模拟前端GUI收集的配置数据（类似crawler_gui_2.py中的get_config_data）
    frontend_config = {
        "input_url": "https://www.bjrd.gov.cn/rdzl/rdzc/rdzd/202505/t20250519_4092352.html",
        "base_url": "https://www.bjrd.gov.cn/rdzl/rdzc/rdzd/",
        "max_pages": "1",
        "list_container_selector": ".ty_gl.container",
        "list_container_type": "CSS",
        "article_item_selector": ".ty_list li",
        "article_item_type": "CSS",
        "title_selector": ".xl_title.clearfix",  # 修复后的格式
        "title_selector_type": "CSS",
        "content_selectors": ".zhengwen;.view",
        "content_type": "CSS",
        "date_selector": ".xl_ly span:first-child",
        "date_selector_type": "CSS",
        "source_selector": ".xl_ly span:last-child",
        "source_selector_type": "CSS",
        "page_suffix": "index_{n}.html",
        "page_suffix_start": 1,
        "url_mode": "relative",
        "browser": "firefox",
        "headless": True,
        "collect_links": True,
        "mode": "fast"
    }
    
    print("前端配置:")
    for key, value in frontend_config.items():
        print(f"  {key}: {value}")
    
    # 模拟前端的参数转换逻辑（类似crawler_gui_2.py中的逻辑）
    diver = {"headless": frontend_config["headless"]}
    
    crawler_config = {
        'input_url': frontend_config['input_url'],
        'base_url': frontend_config['base_url'],
        'max_pages': int(frontend_config['max_pages']) if frontend_config['max_pages'] else None,
        'list_container_selector': frontend_config['list_container_selector'],
        'list_container_type': frontend_config['list_container_type'],
        'article_item_selector': frontend_config['article_item_selector'],
        'article_item_type': frontend_config['article_item_type'],
        'title_selector': frontend_config['title_selector'],  # 关键参数
        'title_selector_type': frontend_config['title_selector_type'],  # 关键参数
        'content_selectors': [s.strip() for s in frontend_config['content_selectors'].split(';') if s.strip()],
        'content_type': frontend_config['content_type'],
        'date_selector': frontend_config['date_selector'],
        'date_selector_type': frontend_config['date_selector_type'],
        'source_selector': frontend_config['source_selector'],
        'source_selector_type': frontend_config['source_selector_type'],
        'page_suffix': frontend_config['page_suffix'],
        'page_suffix_start': frontend_config['page_suffix_start'],
        'url_mode': frontend_config['url_mode'],
        'browser': frontend_config['browser'],
        'diver': diver,
        'collect_links': frontend_config.get('collect_links', True),
        'mode': frontend_config.get('mode', 'balance'),
    }
    
    print("\n转换后的爬虫配置:")
    for key, value in crawler_config.items():
        print(f"  {key}: {value}")
    
    # 验证关键参数
    print(f"\n关键参数验证:")
    print(f"✓ title_selector: '{crawler_config['title_selector']}'")
    print(f"✓ title_selector_type: '{crawler_config['title_selector_type']}'")
    
    return crawler_config

def test_save_article_directly():
    """直接测试save_article函数"""
    print("\n=== 直接测试save_article函数 ===")
    
    # 获取driver
    driver = selenium_diver_change.get_driver(browser="firefox", diver={"headless": True})
    
    try:
        # 测试参数
        test_url = "https://www.bjrd.gov.cn/rdzl/rdzc/rdzd/202505/t20250519_4092352.html"
        save_dir = "./test_output"
        os.makedirs(save_dir, exist_ok=True)
        
        # 调用save_article函数，传入正确的title_selector
        result = crawler.save_article(
            driver=driver,
            link=test_url,
            save_dir=save_dir,
            page_title="frontend_test",
            content_selectors=[".zhengwen", ".view"],
            date_selector=".xl_ly span:first-child",
            source_selector=".xl_ly span:last-child",
            title_selector=".xl_title.clearfix",  # 修复后的选择器
            title_selector_type="CSS",
            mode="fast"
        )
        
        print(f"save_article 结果: {'成功' if result else '失败'}")
        
        # 检查生成的CSV文件
        csv_file = os.path.join(save_dir, "frontend_test.csv")
        if os.path.exists(csv_file):
            print(f"✓ CSV文件已生成: {csv_file}")
            with open(csv_file, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.split('\n')
                if len(lines) >= 2:
                    header = lines[0]
                    data = lines[1]
                    print(f"✓ 表头: {header}")
                    print(f"✓ 数据行: {data[:100]}...")  # 只显示前100个字符
                    
                    # 检查标题字段是否为空
                    data_fields = data.split(',')
                    if len(data_fields) >= 3:
                        title_field = data_fields[2]  # 标题是第3个字段
                        if title_field.strip():
                            print(f"✓ 标题字段不为空: {title_field[:50]}...")
                        else:
                            print("✗ 标题字段为空")
                    else:
                        print("✗ 数据格式异常")
                else:
                    print("✗ CSV文件内容异常")
        else:
            print("✗ CSV文件未生成")
            
    finally:
        driver.quit()

if __name__ == "__main__":
    # 测试参数传递
    config = test_frontend_to_backend_params()
    
    # 直接测试save_article
    test_save_article_directly()
    
    print("\n=== 测试完成 ===")
