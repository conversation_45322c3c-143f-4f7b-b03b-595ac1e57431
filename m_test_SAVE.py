from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import time
import os
import csv
import random
import requests
import txt_clear
from bs4 import BeautifulSoup
import crawler

URL="https://www.rd.suzhou.gov.cn/lfgz/012001/20240126/baa1cded-7e7c-40ef-acf6-cc7941918f9b.html"
title_selector=".news-article h3"
date_selector="div.ewb-article-sources p:nth-of-type(2)"
source_selector="div.ewb-article-sources p:first-child"
content_selectors=["div.ewb-article-info"]






def main():
    import selenium_diver_change
    
    # 测试配置
    test_url = URL
    save_dir = "./test_output"
    os.makedirs(save_dir, exist_ok=True)
    
    driver = selenium_diver_change.get_driver(browser="firefox", diver={"headless": True})
    

    # 测试保存文章
    result = crawler.save_article(
        driver=driver,
        link=test_url,
        save_dir=save_dir,
        page_title="test_page",
        content_selectors=content_selectors,
        date_selector=date_selector,
        date_selector_type="CSS",
        source_selector=source_selector,
        source_selector_type="CSS",
        title_selector=title_selector,
        title_selector_type="CSS",
        mode="fast"  # 使用快速模式测试
    )
    
    print(f"测试结果: {'成功' if result else '失败'}")
    driver.quit()

if __name__ == "__main__":
    main()