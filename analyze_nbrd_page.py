#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析宁波人大网页结构
"""

import requests
from bs4 import BeautifulSoup
import re

def analyze_page(url):
    """分析网页结构"""
    print(f"正在分析网页: {url}")
    
    try:
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
        }
        
        response = requests.get(url, headers=headers, timeout=10)
        response.encoding = response.apparent_encoding
        
        soup = BeautifulSoup(response.text, 'html.parser')
        
        print("\n=== 网页标题 ===")
        title_tag = soup.find('title')
        if title_tag:
            print(f"页面标题: {title_tag.get_text().strip()}")
        
        print("\n=== 查找可能的标题元素 ===")
        # 查找包含"title"的class
        title_elements = soup.find_all(class_=re.compile(r'title', re.I))
        for i, elem in enumerate(title_elements[:5]):
            print(f"{i+1}. 标签: {elem.name}, class: {elem.get('class')}, 内容: {elem.get_text().strip()[:100]}")
        
        # 查找h1-h6标签
        for tag in ['h1', 'h2', 'h3', 'h4', 'h5', 'h6']:
            elements = soup.find_all(tag)
            if elements:
                print(f"{tag.upper()}标签:")
                for elem in elements[:3]:
                    print(f"  内容: {elem.get_text().strip()[:100]}")
        
        print("\n=== 查找可能的内容元素 ===")
        # 查找包含"content"或"article"的class
        content_elements = soup.find_all(class_=re.compile(r'(content|article|cont)', re.I))
        for i, elem in enumerate(content_elements[:5]):
            text = elem.get_text().strip()
            if len(text) > 50:  # 只显示有实际内容的元素
                print(f"{i+1}. 标签: {elem.name}, class: {elem.get('class')}, 内容长度: {len(text)}")
                print(f"   内容预览: {text[:150]}...")
        
        print("\n=== 查找日期和来源信息 ===")
        # 查找包含日期的元素
        date_patterns = [r'\d{4}[-/]\d{1,2}[-/]\d{1,2}', r'\d{4}年\d{1,2}月\d{1,2}日']
        for pattern in date_patterns:
            date_elements = soup.find_all(string=re.compile(pattern))
            if date_elements:
                print(f"找到日期信息:")
                for elem in date_elements[:3]:
                    parent = elem.parent
                    print(f"  内容: {elem.strip()}")
                    print(f"  父元素: {parent.name}, class: {parent.get('class')}")
        
        # 查找包含"时间"或"来源"的元素
        info_elements = soup.find_all(string=re.compile(r'(时间|来源|发布)', re.I))
        if info_elements:
            print(f"找到时间/来源信息:")
            for elem in info_elements[:5]:
                parent = elem.parent
                print(f"  内容: {elem.strip()}")
                print(f"  父元素: {parent.name}, class: {parent.get('class')}")
        
        print("\n=== 查找li元素（可能包含日期来源） ===")
        li_elements = soup.find_all('li')
        for i, li in enumerate(li_elements[:10]):
            text = li.get_text().strip()
            if any(keyword in text for keyword in ['时间', '来源', '发布', '日期']):
                print(f"{i+1}. li内容: {text}")
                print(f"   class: {li.get('class')}")
        
        return soup
        
    except Exception as e:
        print(f"分析网页时出错: {e}")
        return None

if __name__ == "__main__":
    url = "https://www.nbrd.gov.cn/art/2024/12/13/art_1229576425_41379.html"
    analyze_page(url)
