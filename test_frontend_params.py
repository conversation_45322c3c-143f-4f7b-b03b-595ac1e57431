#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试前端参数传递的脚本
"""

import json
import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_config_loading():
    """测试配置文件加载"""
    print("=== 测试配置文件加载 ===")
    
    # 读取配置文件
    with open('config.json', 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    # 检查北京人大配置
    bjrd_config = config['groups']['北京人大']
    print(f"北京人大 title_selector: '{bjrd_config['title_selector']}'")
    print(f"北京人大 title_selector_type: '{bjrd_config['title_selector_type']}'")
    
    # 检查宁波人大配置
    nbrd_config = config['groups']['宁波人大']
    print(f"宁波人大 title_selector: '{nbrd_config['title_selector']}'")
    print(f"宁波人大 title_selector_type: '{nbrd_config['title_selector_type']}'")
    
    # 验证CSS选择器格式
    def validate_css_selector(selector, name):
        if not selector:
            print(f"✓ {name}: 空选择器（正常）")
            return True
        
        # 检查是否有空格但没有点号（常见错误）
        if ' ' in selector and not selector.startswith('.'):
            print(f"✗ {name}: 可能的格式错误 - 包含空格但不以点号开头")
            return False
        
        # 检查多个类名是否正确用点号分隔
        if ' ' in selector and '.' in selector:
            parts = selector.split()
            for part in parts:
                if '.' not in part and part not in ['h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'div', 'span', 'p', 'a']:
                    print(f"✗ {name}: 可能的格式错误 - '{part}' 应该以点号开头")
                    return False
        
        print(f"✓ {name}: 格式看起来正确")
        return True
    
    print("\n=== 验证选择器格式 ===")
    validate_css_selector(bjrd_config['title_selector'], "北京人大 title_selector")
    validate_css_selector(nbrd_config['title_selector'], "宁波人大 title_selector")

def test_crawler_params():
    """测试爬虫参数传递"""
    print("\n=== 测试爬虫参数传递 ===")
    
    # 模拟前端传递的参数
    test_config = {
        'title_selector': '.xl_title.clearfix',
        'title_selector_type': 'CSS',
        'content_selectors': ['.zhengwen', '.view'],
        'date_selector': '.xl_ly span:first-child',
        'source_selector': '.xl_ly span:last-child'
    }
    
    print("模拟的前端配置:")
    for key, value in test_config.items():
        print(f"  {key}: {value}")
    
    # 检查是否有明显的格式问题
    title_sel = test_config['title_selector']
    if title_sel and ' ' in title_sel and not title_sel.replace('.', '').replace(' ', '').replace('h1', '').replace('h2', '').replace('h3', '').replace('div', '').replace('span', '').replace('p', '').replace('a', ''):
        print("✗ title_selector 可能有格式问题")
    else:
        print("✓ title_selector 格式正确")

if __name__ == "__main__":
    test_config_loading()
    test_crawler_params()
    print("\n=== 测试完成 ===")
