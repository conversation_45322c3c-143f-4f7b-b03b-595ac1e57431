from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import time
import os
import csv
import random
import requests
import txt_clear
from bs4 import BeautifulSoup


URL="https://www.bjrd.gov.cn/rdzl/rdzc/rdzd/202505/t20250519_4092352.html"
title_selector="xl_title clearfix"
date_selector=".xl_ly span:first-child" 
source_selector=".xl_ly span:last-child"
content_selectors=["div.TRS_Editor", "div.zhengwen", "div.view"]




def save_article(
    driver, link, save_dir, page_title, content_selectors, date_selector, source_selector,
    content_type="CSS", date_selector_type="CSS", source_selector_type="CSS", collect_links=True, mode="balance", filters=None,
    title_selector=None, title_selector_type="CSS"  
):
    file_path = os.path.join(save_dir, f"{page_title}(test).csv") 
    # 删除已存在的同名文件
    if os.path.exists(file_path):
        try:
            os.remove(file_path)
        except Exception as e:
            print(f"删除旧文件失败: {file_path}, 错误: {str(e)}")
    
    content_text = None
    article_date = ""
    article_source = ""
    img_links = []
    attach_links = []
    article_title = ""  

    # --- 1. 快速模式（requests+bs4） ---
    def fetch_by_requests():
        try:
            headers = {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
            }
            resp = requests.get(link, headers=headers, timeout=10)
            resp.encoding = resp.apparent_encoding
            soup = BeautifulSoup(resp.text, "html.parser")
            content = None
            for selector in content_selectors:
                if content_type == "XPath":
                    continue
                content = soup.select_one(selector)
                if content:
                    break
            # 标题提取
            page_title_val = ""  # 初始化为空字符串
            if title_selector:
                try:
                    if title_selector_type == "XPath":
                        pass
                    else:
                        title_elem = soup.select_one(title_selector)
                        if title_elem:
                            page_title_val = title_elem.get_text(strip=True)
                except Exception:
                    pass
            page_title_val = txt_clear.clean_title(page_title_val)
            if content:
                html = str(content)
                text = txt_clear.clean_html_content(html)
                imgs, attaches = [], []
                if collect_links:
                    imgs = [img['src'] for img in content.find_all("img") if img.get("src")]
                    for a in content.find_all("a", href=True):
                        href = a['href']
                        if any(href.lower().endswith(ext) for ext in [".pdf", ".doc", ".docx", ".zip", ".rar", ".xls", ".xlsx"]):
                            attaches.append(href)
                date, source = "", ""
                if date_selector and date_selector_type == "CSS":
                    date_elem = soup.select_one(date_selector)
                    if date_elem:
                        date_text = date_elem.get_text(strip=True)
                        if "日期：" in date_text:
                            date = date_text.split("日期：")[-1].strip().split()[0]
                        else:
                            date = date_text
                        date = txt_clear.normalize_date(date)
                if source_selector and source_selector_type == "CSS":
                    source_elems = soup.select(source_selector)
                    if len(source_elems) > 1:
                        source_text = source_elems[1].get_text(strip=True)
                        if "来源：" in source_text:
                            source = source_text.split("来源：")[-1].strip()
                        else:
                            source = source_text
                    elif source_elems:
                        source = source_elems[0].get_text(strip=True)
                    else:
                        source = "本站"
                    source = txt_clear.normalize_source(source)
                return text, date, source, imgs, attaches, page_title_val
            else:
                return None, "", "", [], [], page_title_val
        except Exception:
            return None, "", "", [], [], ""
    # --- 2. 安全模式（selenium） ---
    def fetch_by_selenium():
        try:
            if len(driver.window_handles) >= 4:
                driver.switch_to.window(driver.window_handles[0])
                driver.close()
                driver.switch_to.window(driver.window_handles[-1])
            driver.execute_script("window.open('');")
            driver.switch_to.window(driver.window_handles[-1])
            driver.get(link)
            content = None
            for selector in content_selectors:
                try:
                    if content_type == "XPath":
                        content = WebDriverWait(driver, 5).until(
                            EC.presence_of_element_located((By.XPATH, selector))
                        )
                    else:
                        content = WebDriverWait(driver, 5).until(
                            EC.presence_of_element_located((By.CSS_SELECTOR, selector))
                        )
                    break
                except Exception:
                    continue
                title = ""
            try:
                if title_selector:
                    if title_selector_type == "XPath":
                        title_elem = driver.find_element(By.XPATH, title_selector)
                    else:
                        title_elem = driver.find_element(By.CSS_SELECTOR, title_selector)
                    title_text = title_elem.text.strip()
                    title = txt_clear.clean_title(title_text)
            except Exception:
                pass
            if not content:
                driver.close()
                driver.switch_to.window(driver.window_handles[0])
                return None, "", "", [], [], title
            html = content.get_attribute("innerHTML")
            text = txt_clear.clean_html_content(html)
            imgs, attaches = [], []
            if collect_links:
                img_elements = content.find_elements(By.TAG_NAME, "img")
                imgs = [img.get_attribute("src") for img in img_elements if img.get_attribute("src")]
                attach_elements = content.find_elements(By.TAG_NAME, "a")
                for a in attach_elements:
                    href = a.get_attribute("href")
                    if href and any(href.lower().endswith(ext) for ext in [".pdf", ".doc", ".docx", ".zip", ".rar", ".xls", ".xlsx"]):
                        attaches.append(href)
            date, source = "", ""
            try:
                if date_selector:
                    if date_selector_type == "XPath":
                        date_elem = driver.find_element(By.XPATH, date_selector)
                    else:
                        date_elem = driver.find_element(By.CSS_SELECTOR, date_selector)
                    date_text = date_elem.text.strip()
                    if "日期：" in date_text:
                        date = date_text.split("日期：")[-1].strip().split()[0]
                    else:
                        date = date_text
                    date = txt_clear.normalize_date(date)
            except Exception:
                date = ""
            try:
                if source_selector:
                    if source_selector_type == "XPath":
                        source_elems = driver.find_elements(By.XPATH, source_selector)
                    else:
                        source_elems = driver.find_elements(By.CSS_SELECTOR, source_selector)
                if len(source_elems) > 1:
                    source_text = source_elems[1].text.strip()
                    if "来源：" in source_text:
                        source = source_text.split("来源：")[-1].strip()
                    else:
                        source = source_text
                elif source_elems:
                    source = source_elems[0].text.strip()
                source = txt_clear.normalize_source(source)
            except Exception:
                source = ""
            driver.close()
            driver.switch_to.window(driver.window_handles[0])
            time.sleep(random.uniform(0.1, 0.5))
            return text, date, source, imgs, attaches, title
        except Exception:
            try:
                driver.close()
                driver.switch_to.window(driver.window_handles[0])
            except Exception:
                pass
            return None, "", "", [], [], ""
    # --- 3. 选择模式 ---
    if mode == "fast":
        content_text, article_date, article_source, img_links, attach_links, article_title = fetch_by_requests()
    elif mode == "safe":
        content_text, article_date, article_source, img_links, attach_links, article_title = fetch_by_selenium()
    else:  # balance
        content_text, article_date, article_source, img_links, attach_links, article_title = fetch_by_requests()
        if not content_text:
            content_text, article_date, article_source, img_links, attach_links, article_title = fetch_by_selenium()
    # 集成内容过滤
    content_text = txt_clear.filter_content(content_text, filters)
    # 合并图片和附件链接
    all_links = []
    if collect_links:
        if img_links:
            all_links.append("图片链接: " + ", ".join(img_links))
        if attach_links:
            all_links.append("附件链接: " + ", ".join(attach_links))
        if all_links and content_text:
            content_text += "[" + " | ".join(all_links) + "]"
    # 在写入CSV前检查并设置默认来源
    if not article_source:  # 如果来源为空
        article_source = "本站"  # 设置为默认值"本站"
    # 不再兜底标题，标题为空就为空
    write_header = not os.path.exists(file_path)
    try:
        with open(file_path, 'a', encoding='utf-8', newline='') as f:
            writer = csv.writer(f)
            if write_header:
                writer.writerow(['日期', '来源', '标题', '链接', '内容'])
            writer.writerow([article_date, article_source, article_title, link, content_text])
        print(f"已保存: {article_title}")
        return True
    except Exception as e:
        print(f"保存文章时出错: {article_title}, 错误: {str(e)}")
        return False


def main():
    import selenium_diver_change
    
    # 测试配置
    test_url = URL
    save_dir = "./test_output"
    os.makedirs(save_dir, exist_ok=True)
    
    driver = selenium_diver_change.get_driver(browser="firefox", diver={"headless": True})
    

    # 测试保存文章
    result = save_article(
        driver=driver,
        link=test_url,
        save_dir=save_dir,
        page_title="test_page",
        content_selectors=content_selectors,
        date_selector=date_selector,  # 修正变量名
        source_selector=source_selector,
        title_selector=title_selector,
        mode="fast"  # 使用快速模式测试
    )
    
    print(f"测试结果: {'成功' if result else '失败'}")
    driver.quit()

if __name__ == "__main__":
    main()