from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import re
import time
import os
import csv
import random
import requests
import txt_clear
from bs4 import BeautifulSoup
from urllib.parse import urljoin
import selenium_diver_change

print("Loaded crawler.py from:", __file__)

# 创建保存文章的目录
def create_save_dir(page_title):
    base_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "articles")
    if not os.path.exists(base_dir):
        os.makedirs(base_dir)
    page_folder = "".join(c for c in page_title if c.isalnum() or c in (' ','-','_'))
    save_dir = os.path.join(base_dir, page_folder)
    if not os.path.exists(save_dir):
        os.makedirs(save_dir)
    return save_dir

# 获取文章链接
def get_article_links(driver, input_url, list_container_selector, article_item_selector, list_container_type="CSS", article_item_type="CSS", title_selector=None, title_selector_type="CSS"):
    try:
        driver.get(input_url)
    except Exception as e:
        raise RuntimeError(f"无法访问目标URL: {input_url}，错误: {e}")
    page_title = driver.title
    print(f"正在爬取页面：{page_title}")
    try:
        # 支持多个列表容器
        if list_container_type == "XPath":
            main_containers = WebDriverWait(driver, 10).until(
                EC.presence_of_all_elements_located((By.XPATH, list_container_selector))
            )
        else:
            main_containers = WebDriverWait(driver, 10).until(
                EC.presence_of_all_elements_located((By.CSS_SELECTOR, list_container_selector))
            )
    except Exception as e:
        raise RuntimeError(f"页面结构异常，未找到列表容器: {list_container_selector}，错误: {e}")
    article_elements = []
    article_links = []
    article_titles = []
    for container in main_containers:
        try:
            if article_item_type == "XPath":
                articles = container.find_elements(By.XPATH, article_item_selector)
            else:
                articles = container.find_elements(By.CSS_SELECTOR, article_item_selector)
            article_elements.extend(articles)
            for article in articles:
                # 优先取 article 本身的 href
                href = article.get_attribute('href')
                if not href:
                    # 若没有，再取其下第一个 a 标签的 href
                    try:
                        a_tag = article.find_element(By.TAG_NAME, 'a')
                        href = a_tag.get_attribute('href')
                    except Exception:
                        href = None
                if href:
                    article_links.append(href)
                    article_titles.append(article.text.strip())
        except Exception:
            continue
    print(f"找到 {len(article_links)} 个有效链接")
    return article_elements, page_title, article_links, article_titles

# 获取完整的链接
def get_full_link(href, input_url, base_url, url_mode):
    """
    url_mode: 'absolute' 绝对路径，'relative' 相对路径（base_url+href）
    """
    if not href:
        return ''
    # 绝对URL
    if href.startswith(('http://', 'https://')):
        return href
    # 协议相对URL
    if href.startswith('//'):
        scheme = base_url.split(':')[0] if ':' in base_url else 'https'
        return f"{scheme}:{href}"
    # 锚点
    if href.startswith('#'):
        return urljoin(base_url, href)
    # 相对路径
    if url_mode == "absolute":
        return urljoin(input_url, href)
    elif url_mode == "relative":
        return urljoin(base_url, href)
    else:
        return urljoin(base_url, href)
    
# 保存文章内容到文件
def save_article(
    driver, link, save_dir, page_title, content_selectors, date_selector, source_selector,
    content_type="CSS", date_selector_type="CSS", source_selector_type="CSS", collect_links=True, mode="balance", filters=None,
    title_selector=None, title_selector_type="CSS"  # 移除title参数
):
    file_path = os.path.join(save_dir, f"{page_title}.csv") 
    content_text = None
    article_date = ""
    article_source = ""
    img_links = []
    attach_links = []
    article_title = ""  # 初始化为空字符串

    # --- 1. 快速模式（requests+bs4） ---
    def fetch_by_requests():
        try:
            headers = {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
            }
            resp = requests.get(link, headers=headers, timeout=10)
            resp.encoding = resp.apparent_encoding
            soup = BeautifulSoup(resp.text, "html.parser")
            content = None
            for selector in content_selectors:
                if content_type == "XPath":
                    continue
                content = soup.select_one(selector)
                if content:
                    break
            # 标题提取
            page_title_val = ""  # 初始化为空字符串
            if title_selector:
                try:
                    if title_selector_type == "XPath":
                        pass
                    else:
                        title_elem = soup.select_one(title_selector)
                        if title_elem:
                            page_title_val = title_elem.get_text(strip=True)
                except Exception:
                    pass
            page_title_val = txt_clear.clean_title(page_title_val)
            if content:
                html = str(content)
                text = txt_clear.clean_html_content(html)
                imgs, attaches = [], []
                if collect_links:
                    imgs = [img['src'] for img in content.find_all("img") if img.get("src")]
                    for a in content.find_all("a", href=True):
                        href = a['href']
                        if any(href.lower().endswith(ext) for ext in [".pdf", ".doc", ".docx", ".zip", ".rar", ".xls", ".xlsx"]):
                            attaches.append(href)
                date, source = "", ""
                if date_selector and date_selector_type == "CSS":
                    date_elem = soup.select_one(date_selector)
                    if date_elem:
                        date_text = date_elem.get_text(strip=True)
                        if "日期：" in date_text:
                            date = date_text.split("日期：")[-1].strip().split()[0]
                        else:
                            date = date_text
                        date = txt_clear.normalize_date(date)
                if source_selector and source_selector_type == "CSS":
                    source_elems = soup.select(source_selector)
                    if len(source_elems) > 1:
                        source_text = source_elems[1].get_text(strip=True)
                        if "来源：" in source_text:
                            source = source_text.split("来源：")[-1].strip()
                        else:
                            source = source_text
                    elif source_elems:
                        source = source_elems[0].get_text(strip=True)
                    else:
                        source = "本站"
                    source = txt_clear.normalize_source(source)
                return text, date, source, imgs, attaches, page_title_val
            else:
                return None, "", "", [], [], page_title_val
        except Exception:
            return None, "", "", [], [], ""
    # --- 2. 安全模式（selenium） ---
    def fetch_by_selenium():
        try:
            if len(driver.window_handles) >= 4:
                driver.switch_to.window(driver.window_handles[0])
                driver.close()
                driver.switch_to.window(driver.window_handles[-1])
            driver.execute_script("window.open('');")
            driver.switch_to.window(driver.window_handles[-1])
            driver.get(link)
            content = None
            for selector in content_selectors:
                try:
                    if content_type == "XPath":
                        content = WebDriverWait(driver, 5).until(
                            EC.presence_of_element_located((By.XPATH, selector))
                        )
                    else:
                        content = WebDriverWait(driver, 5).until(
                            EC.presence_of_element_located((By.CSS_SELECTOR, selector))
                        )
                    break
                except Exception:
                    continue
                title = ""
            try:
                if title_selector:
                    if title_selector_type == "XPath":
                        title_elem = driver.find_element(By.XPATH, title_selector)
                    else:
                        title_elem = driver.find_element(By.CSS_SELECTOR, title_selector)
                    title_text = title_elem.text.strip()
                    title = txt_clear.clean_title(title_text)
            except Exception:
                pass
            if not content:
                driver.close()
                driver.switch_to.window(driver.window_handles[0])
                return None, "", "", [], [], title
            html = content.get_attribute("innerHTML")
            text = txt_clear.clean_html_content(html)
            imgs, attaches = [], []
            if collect_links:
                img_elements = content.find_elements(By.TAG_NAME, "img")
                imgs = [img.get_attribute("src") for img in img_elements if img.get_attribute("src")]
                attach_elements = content.find_elements(By.TAG_NAME, "a")
                for a in attach_elements:
                    href = a.get_attribute("href")
                    if href and any(href.lower().endswith(ext) for ext in [".pdf", ".doc", ".docx", ".zip", ".rar", ".xls", ".xlsx"]):
                        attaches.append(href)
            date, source = "", ""
            try:
                if date_selector:
                    if date_selector_type == "XPath":
                        date_elem = driver.find_element(By.XPATH, date_selector)
                    else:
                        date_elem = driver.find_element(By.CSS_SELECTOR, date_selector)
                    date_text = date_elem.text.strip()
                    if "日期：" in date_text:
                        date = date_text.split("日期：")[-1].strip().split()[0]
                    else:
                        date = date_text
                    date = txt_clear.normalize_date(date)
            except Exception:
                date = ""
            try:
                if source_selector:
                    if source_selector_type == "XPath":
                        source_elems = driver.find_elements(By.XPATH, source_selector)
                    else:
                        source_elems = driver.find_elements(By.CSS_SELECTOR, source_selector)
                if len(source_elems) > 1:
                    source_text = source_elems[1].text.strip()
                    if "来源：" in source_text:
                        source = source_text.split("来源：")[-1].strip()
                    else:
                        source = source_text
                elif source_elems:
                    source = source_elems[0].text.strip()
                source = txt_clear.normalize_source(source)
            except Exception:
                source = ""
            driver.close()
            driver.switch_to.window(driver.window_handles[0])
            time.sleep(random.uniform(0.1, 0.5))
            return text, date, source, imgs, attaches, title
        except Exception:
            try:
                driver.close()
                driver.switch_to.window(driver.window_handles[0])
            except Exception:
                pass
            return None, "", "", [], [], ""
    # --- 3. 选择模式 ---
    if mode == "fast":
        content_text, article_date, article_source, img_links, attach_links, article_title = fetch_by_requests()
    elif mode == "safe":
        content_text, article_date, article_source, img_links, attach_links, article_title = fetch_by_selenium()
    else:  # balance
        content_text, article_date, article_source, img_links, attach_links, article_title = fetch_by_requests()
        if not content_text:
            content_text, article_date, article_source, img_links, attach_links, article_title = fetch_by_selenium()
    # 集成内容过滤
    content_text = txt_clear.filter_content(content_text, filters)
    # 合并图片和附件链接
    all_links = []
    if collect_links:
        if img_links:
            all_links.append("图片链接: " + ", ".join(img_links))
        if attach_links:
            all_links.append("附件链接: " + ", ".join(attach_links))
        if all_links and content_text:
            content_text += "[" + " | ".join(all_links) + "]"
    # 在写入CSV前检查并设置默认来源
    if not article_source:  # 如果来源为空
        article_source = "本站"  # 设置为默认值"本站"
    # 不再兜底标题，标题为空就为空
    write_header = not os.path.exists(file_path)
    try:
        with open(file_path, 'a', encoding='utf-8', newline='') as f:
            writer = csv.writer(f)
            if write_header:
                writer.writerow(['日期', '来源', '标题', '链接', '内容'])
            writer.writerow([article_date, article_source, article_title, link, content_text])
        print(f"已保存: {article_title}")
        return True
    except Exception as e:
        print(f"保存文章时出错: {article_title}, 错误: {str(e)}")
        return False

# 爬取文章
def get_page_url(input_url, page_num, page_suffix, page_suffix_next):
    """
    根据页码生成对应的页面URL
    优化点：
    1. 统一处理URL拼接逻辑
    2. 支持更多URL格式
    3. 添加参数校验
    """
    if not input_url or page_num < 1:
        raise ValueError("无效的输入URL或页码")
    
    # 处理基础URL
    url_base = input_url.rstrip('/')
    if url_base.endswith('.html'):
        url_base = url_base[:-5]
    
    # 确定后缀
    if page_num == 1:
        return url_base
    
    suffix = page_suffix_next if (page_num > 2 and page_suffix_next) else page_suffix
    
    # 处理{n}占位符
    if "{n}" in suffix:
        suffix = suffix.format(n=page_num)
    
    # 统一拼接逻辑
    if suffix.startswith(('http://', 'https://')):
        return suffix
    elif suffix.startswith('/'):
        return f"{url_base}{suffix}"
    else:
        return f"{url_base}/{suffix}"

def crawl_articles(input_url, base_url, max_pages=None, 
                  list_container_selector=".main",
                  list_container_type="CSS",
                  article_item_selector=".clearfix.ty_list li a",
                  article_item_type="CSS",
                  title_selector=None,
                  title_selector_type="CSS",
                  content_selectors=[
                      "div.view.TRS_UEDITOR.trs_paper_default.trs_web",
                      ".TRS_Editor",
                      "div.zhengwen"
                  ],
                  content_type="CSS",
                  date_selector="div.xl_ly.fl > span",
                  date_selector_type="CSS",
                  source_selector="div.xl_ly.fl > span",
                  source_selector_type="CSS",
                  log_callback=None,
                  page_suffix="index_{n}.html",
                  page_suffix_start=1,
                  url_mode="absolute",
                  browser="firefox",
                  diver=None,
                  collect_links=True,   # 新增参数
                  mode="balance",      # 新增：采集模式
                  filters=None          # 新增：内容过滤
                  ):
    """
    url_mode: 'absolute' 绝对路径，'relative' 相对路径（base_url+href）
    collect_links: 是否采集正文图片和附件链接
    mode: fast（只用requests+bs4），safe（只用selenium），balance（先requests失败再selenium，默认）
    filters: 过滤关键词或正则表达式列表，匹配到的行将被移除
    """
    if log_callback:
        log_callback(f"开始爬取任务: {input_url}")
    # 处理页面加载策略
    page_load_strategy = None
    if diver and diver.get('page_load_strategy'):
        page_load_strategy = diver['page_load_strategy']
    driver = selenium_diver_change.get_driver(browser=browser, diver=diver)
    processed_titles = set()
    all_article_info = []
    found_urls = set()
    page_num = 1
    total_articles = 0
    while True:
        if max_pages and page_num > max_pages:
            msg = f"已达到最大页数限制: {max_pages}"
            if log_callback:
                log_callback(msg)
            else:
                print(msg)
            break
        if page_num == 1:
            page_url = input_url
            if log_callback:
                log_callback(f"[DEBUG] page_num={page_num}, page_url={page_url}")
            else:
                print(f"[DEBUG] page_num={page_num}, page_url={page_url}")
        else:
            url_base = base_url
            if url_base.endswith('.html'):
                url_base = url_base[:-5]
            n = page_suffix_start + (page_num - 2)
            suffix = page_suffix
            if "{n}" in suffix:
                suffix = suffix.format(n=n)
            if not url_base.endswith('/') and not suffix.startswith('/') and not suffix.startswith('index') and not suffix.startswith('_'):
                page_url = f"{url_base}/{suffix}"
            else:
                page_url = f"{url_base}{suffix}"
            if log_callback:
                log_callback(f"[DEBUG] page_num={page_num}, page_url={page_url}")
            else:
                print(f"[DEBUG] page_num={page_num}, page_url={page_url}")
        try:
            # 根据页面加载策略决定等待方式
            if page_load_strategy == "none":
                driver.execute_script("window.stop();")
            elif page_load_strategy == "eager":
                WebDriverWait(driver, 10).until(lambda d: d.execute_script('return document.readyState') in ['interactive', 'complete'])
            else:
                WebDriverWait(driver, 15).until(lambda d: d.execute_script('return document.readyState') == 'complete')
            articles, page_title, article_links, article_titles = get_article_links(
                driver, page_url, list_container_selector, article_item_selector, list_container_type, article_item_type, title_selector, title_selector_type)
            total_articles += len(articles)
        except Exception as e:
            msg = f"获取文章链接时出错: {str(e)}"
            if log_callback:
                log_callback(msg)
            else:
                print(msg)
            if "无法访问目标URL" in str(e):
                msg2 = f"请检查网络连接或目标网站是否可访问: {page_url}"
                if log_callback:
                    log_callback(msg2)
                else:
                    print(msg2)
            elif "未找到列表容器" in str(e) or "未找到文章项" in str(e):
                msg2 = f"页面结构可能已变动，请检查选择器设置: {list_container_selector} / {article_item_selector}"
                if log_callback:
                    log_callback(msg2)
                else:
                    print(msg2)
                # 新增：等待3秒后重试一次
                time.sleep(3)
                try:
                    articles, page_title, article_links, article_titles = get_article_links(
                        driver, page_url, list_container_selector, article_item_selector, list_container_type, article_item_type, title_selector, title_selector_type)
                    total_articles += len(articles)
                except Exception as e2:
                    msg3 = f"重试后依然失败: {str(e2)}"
                    if log_callback:
                        log_callback(msg3)
                    else:
                        print(msg3)
                    break
            else:
                break
        if not articles:
            msg = "没有更多文章，采集结束。"
            if log_callback:
                log_callback(msg)
            else:
                print(msg)
            break
        save_dir = create_save_dir(page_title)
        new_found = False
        for article, href, title in zip(articles, article_links, article_titles):
            if not href:
                continue
            full_url = get_full_link(href, page_url, base_url, url_mode)
            found_urls.add(full_url)
            if title in processed_titles:
                continue
            all_article_info.append((title, href, save_dir, page_title, page_url))
            new_found = True
            processed_titles.add(title)
        if not new_found:
            msg = "没有新文章。"
            if log_callback:
                log_callback(msg)
            else:
                print(msg)
            break
        page_num += 1
    if log_callback:
        log_callback(f"共找到 {total_articles} 篇文章 ")
    else:
        print(f"共找到 {total_articles} 篇文章 ")   
    success_count = 0
    fail_count = 0
    total_to_process = len(all_article_info)
    for idx, (title, href, save_dir, page_title, page_url) in enumerate(all_article_info, 1):
        percent = (idx / total_to_process) * 100
        status_msg = f"正在处理第{idx}/{total_to_process}篇文章 ({percent:.1f}%): {title}"
        if log_callback:
            log_callback(status_msg)
        else:
            print(status_msg)
        link = get_full_link(href, page_url, base_url, url_mode)
        try:
            success = save_article(driver, link, save_dir, page_title,  # 修改这里：移除title参数
                                  content_selectors, date_selector, source_selector, 
                                  content_type, date_selector_type, source_selector_type,
                                  collect_links=collect_links, mode=mode, filters=filters,
                                  title_selector=title_selector, title_selector_type=title_selector_type)
            if success:
                success_count += 1
            else:
                fail_count += 1
        except Exception as e:
            error_msg = f"处理文章时出错: {title}, 错误: {str(e)}"
            if log_callback:
                log_callback(error_msg)
            else:
                print(error_msg)
            fail_count += 1
    driver.quit()
    total = success_count + fail_count
    summary = f"下载完成！共处理 {total} 篇文章\n成功: {success_count} 篇\n失败: {fail_count} 篇"
    if log_callback:
        log_callback(summary)
    else:
        print(summary)
    return {
        "total": total,
        "success": success_count,
        "failed": fail_count
    }